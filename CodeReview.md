1. The ship is inside the planet and not that visible when at a system on the star map. Change it so that it's just beside the system.

2. Add the following to the game:

// ===========================================
// OVERARCHING ENDGAME GOAL: THE VOID STORM
// ===========================================

export const ENDGAME_SCENARIO = {
  title: "The Void Storm Crisis",
  description: `
    Ancient sensors across the galaxy have detected an approaching cosmic phenomenon known as the Void Storm - 
    a reality-warping tempest that devours entire star systems, leaving nothing but empty space in its wake. 
    
    The storm originates from beyond the galactic rim and grows stronger with each system it consumes. 
    At its current rate, it will reach the galactic core within 100 cycles, ending all life as we know it.
    
    Legends speak of the Nexus Codex - five ancient artifacts created by the first spacefaring civilization, 
    the Architects. When combined, these artifacts can supposedly activate the Stellar Aegis, a galaxy-spanning 
    defense network hidden within pulsars and neutron stars.
    
    Your mission: Find the five Nexus Fragments, unlock the secrets of the Architects, and activate the 
    Stellar Aegis before the Void Storm consumes everything you hold dear.
  `,
  objectives: [
    "Locate and collect all 5 Nexus Fragments scattered across the galaxy",
    "Decipher Architect ruins to learn the activation sequence",
    "Navigate to the galactic core's hidden Nexus Chamber", 
    "Survive the final confrontation with Void Storm entities",
    "Activate the Stellar Aegis to save the galaxy"
  ],
  nexusFragments: [
    {
      id: "nexus_fragment_1",
      name: "Fragment of Genesis",
      location: "Hidden in the Birthworld Nebula",
      power: "Controls stellar ignition"
    },
    {
      id: "nexus_fragment_2", 
      name: "Fragment of Harmony",
      location: "Guarded by the Crystalline Sentinels",
      power: "Balances cosmic forces"
    },
    {
      id: "nexus_fragment_3",
      name: "Fragment of Memory", 
      location: "Within the Archive Worlds of the Keepers",
      power: "Stores universal knowledge"
    },
    {
      id: "nexus_fragment_4",
      name: "Fragment of Infinity",
      location: "At the edge of the galactic void",
      power: "Manipulates space-time"
    },
    {
      id: "nexus_fragment_5",
      name: "Fragment of Unity",
      location: "The galactic core's Nexus Chamber",
      power: "Binds all fragments together"
    }
  ]
};

// ===========================================
// EXPANDED SPECIES (7 additional races)
// ===========================================

export const EXPANDED_RACES = [
  {
    id: 'vorthak',
    name: 'Vorthak',
    description: 'Silicon-based warriors from high-gravity worlds. Their crystalline exoskeletons make them incredibly durable.',
    bonus: 'All crew members have +2 survivability in combat situations.',
  },
  {
    id: 'etherean',
    name: 'Etherean',
    description: 'Energy beings who exist partially in subspace. Masters of interdimensional technology.',
    bonus: 'Ships can phase through asteroids and spatial anomalies without damage.',
  },
  {
    id: 'mycelian',
    name: 'Mycelian Network',
    description: 'Fungal collective consciousness that communicates through bio-neural networks across space.',
    bonus: 'Instant communication with all friendly ships regardless of distance.',
  },
  {
    id: 'chromarch',
    name: 'Chromarch',
    description: 'Shapeshifting species that can alter their molecular structure. Excellent infiltrators and diplomats.',
    bonus: 'Can recruit crew members from any species encountered during exploration.',
  },
  {
    id: 'nexari',
    name: 'Nexari',
    description: 'Ancient machine-organic hybrids who merge biological intuition with computational precision.',
    bonus: 'All ship systems have 50% reduced power consumption.',
  },
  {
    id: 'stellarborn',
    name: 'Stellarborn',
    description: 'Born in the coronas of stars, they harness stellar energy directly into their technology.',
    bonus: 'Weapons deal 25% more damage and recharge shields 50% faster near stars.',
  },
  {
    id: 'voidwalker',
    name: 'Voidwalker',
    description: 'Mysterious beings from dark space who understand the deepest secrets of the universe.',
    bonus: 'Can detect hidden artifacts and anomalies from twice the normal distance.',
  }
];

// ===========================================
// EXPANDED ARTIFACTS (5 of each type)
// ===========================================

// Power-related Artifacts
export const POWER_ARTIFACTS = [
  {
    id: 'art_power_cell_2',
    name: 'Quantum Resonance Core',
    description: 'A pulsing core that seems to exist in multiple dimensions simultaneously.',
    type: 'artifact',
    effectDescription: 'Increases ship max power by 35 and provides slow power regeneration.',
    powerDraw: -35,
    mass: 8,
    bonus: { stat: 'power.max', value: 35, operator: 'add' }
  },
  {
    id: 'art_power_cell_3',
    name: 'Stellar Fragment',
    description: 'A contained piece of star-matter that burns without fuel for millennia.',
    type: 'artifact',
    effectDescription: 'Increases max power by 25 and boosts weapon damage by 10%.',
    powerDraw: -25,
    mass: 12,
    bonus: { stat: 'power.max', value: 25, operator: 'add' }
  },
  {
    id: 'art_power_cell_4',
    name: 'Void Capacitor',
    description: 'This device seems to draw energy from the empty space between atoms.',
    type: 'artifact',
    effectDescription: 'Provides 15 power and makes all systems 20% more efficient.',
    powerDraw: -15,
    mass: 4,
    bonus: { stat: 'power.max', value: 15, operator: 'add' }
  },
  {
    id: 'art_power_cell_5',
    name: 'Architect Power Matrix',
    description: 'An impossibly complex lattice of energy that defies known physics.',
    type: 'artifact',
    effectDescription: 'Increases max power by 50 but randomly fluctuates output.',
    powerDraw: -50,
    mass: 15,
    bonus: { stat: 'power.max', value: 50, operator: 'add' }
  }
];

// Navigation-related Artifacts  
export const NAVIGATION_ARTIFACTS = [
  {
    id: 'art_nav_matrix_2',
    name: 'Hyperspace Compass',
    description: 'Points toward the nearest significant gravitational anomaly or ancient structure.',
    type: 'artifact',
    effectDescription: 'Reduces fuel consumption by 2 and reveals hidden locations.',
    powerDraw: 3,
    mass: 3,
    bonus: { stat: 'fuelConsumption', value: -2, operator: 'add' }
  },
  {
    id: 'art_nav_matrix_3',
    name: 'Quantum Pathfinder',
    description: 'Calculates the most efficient routes through folded space-time.',
    type: 'artifact',
    effectDescription: 'Fuel consumption reduced by 3 units and movement speed increased.',
    powerDraw: 5,
    mass: 4,
    bonus: { stat: 'fuelConsumption', value: -3, operator: 'add' }
  },
  {
    id: 'art_nav_matrix_4',
    name: 'Dimensional Anchor',
    description: 'Stabilizes your position in normal space, preventing drift and displacement.',
    type: 'artifact',
    effectDescription: 'Eliminates fuel consumption penalties from hazardous sectors.',
    powerDraw: 4,
    mass: 6,
    bonus: { stat: 'fuelConsumption', value: -1, operator: 'add' }
  },
  {
    id: 'art_nav_matrix_5',
    name: 'Architect Star Chart',
    description: 'An ancient navigation aid that knows routes through space that no longer exist.',
    type: 'artifact',
    effectDescription: 'Reduces all travel fuel costs by 4 and unlocks secret passages.',
    powerDraw: 6,
    mass: 5,
    bonus: { stat: 'fuelConsumption', value: -4, operator: 'add' }
  }
];

// Combat-related Artifacts
export const COMBAT_ARTIFACTS = [
  {
    id: 'art_combat_1',
    name: 'Rage Protocol Chip',
    description: 'A military AI subroutine that optimizes weapon targeting and firing sequences.',
    type: 'artifact',
    effectDescription: 'All weapons gain +30% accuracy and +5 damage.',
    powerDraw: 8,
    mass: 2,
    bonus: { stat: 'weapon.damage', value: 5, operator: 'add' }
  },
  {
    id: 'art_combat_2',
    name: 'Kinetic Amplifier',
    description: 'Increases the velocity and impact force of all projectile weapons.',
    type: 'artifact',
    effectDescription: 'Weapon damage increased by 25% and range extended by 2.',
    powerDraw: 12,
    mass: 7,
    bonus: { stat: 'weapon.damage', value: 8, operator: 'add' }
  },
  {
    id: 'art_combat_3',
    name: 'Phase Variance Generator',
    description: 'Causes weapons to shift between dimensions, bypassing shields partially.',
    type: 'artifact',
    effectDescription: 'Weapons ignore 25% of enemy shields and deal +3 damage.',
    powerDraw: 10,
    mass: 5,
    bonus: { stat: 'weapon.damage', value: 3, operator: 'add' }
  },
  {
    id: 'art_combat_4',
    name: 'Berserker Core',
    description: 'Ancient Vorthak technology that enhances aggression and combat effectiveness.',
    type: 'artifact',
    effectDescription: 'Damage increases by 50% when shields are below 25%.',
    powerDraw: 15,
    mass: 8,
    bonus: { stat: 'weapon.damage', value: 10, operator: 'add' }
  },
  {
    id: 'art_combat_5',
    name: 'Architect War-Node',
    description: 'A fragment of the ancient defense network, still programmed to destroy threats.',
    type: 'artifact',
    effectDescription: 'All weapons gain +15 damage and can target multiple enemies.',
    powerDraw: 20,
    mass: 12,
    bonus: { stat: 'weapon.damage', value: 15, operator: 'add' }
  }
];

// Defense-related Artifacts
export const DEFENSE_ARTIFACTS = [
  {
    id: 'art_defense_1',
    name: 'Adaptive Plating',
    description: 'Hull plating that learns from damage and becomes resistant to repeated attacks.',
    type: 'artifact',
    effectDescription: 'Hull regenerates 5 points per turn and gains damage resistance.',
    powerDraw: 6,
    mass: 10,
    bonus: { stat: 'hull.max', value: 25, operator: 'add' }
  },
  {
    id: 'art_defense_2',
    name: 'Quantum Mirror Shield',
    description: 'Reflects a portion of incoming energy attacks back at the attacker.',
    type: 'artifact',
    effectDescription: 'Reflects 15% of energy damage and +50 shield capacity.',
    powerDraw: 8,
    mass: 6,
    bonus: { stat: 'shield.capacity', value: 50, operator: 'add' }
  },
  {
    id: 'art_defense_3',
    name: 'Null-Field Generator',
    description: 'Creates a localized area where energy weapons lose cohesion.',
    type: 'artifact',
    effectDescription: 'Reduces all incoming damage by 20% and +30 shield capacity.',
    powerDraw: 12,
    mass: 8,
    bonus: { stat: 'shield.capacity', value: 30, operator: 'add' }
  },
  {
    id: 'art_defense_4',
    name: 'Regenerative Matrix',
    description: 'Nanotechnology that continuously repairs ship systems during combat.',
    type: 'artifact',
    effectDescription: 'Heals 10 hull and 15 shields per turn during combat.',
    powerDraw: 15,
    mass: 9,
    bonus: { stat: 'hull.max', value: 40, operator: 'add' }
  },
  {
    id: 'art_defense_5',
    name: 'Architect Aegis Core',
    description: 'A piece of the legendary Stellar Aegis, providing ultimate protection.',
    type: 'artifact',
    effectDescription: 'Massive defensive bonuses: +100 shields, +50 hull, 30% damage reduction.',
    powerDraw: 25,
    mass: 15,
    bonus: { stat: 'shield.capacity', value: 100, operator: 'add' }
  }
];

// Utility Artifacts
export const UTILITY_ARTIFACTS = [
  {
    id: 'art_utility_1',
    name: 'Resource Transmuter',
    description: 'Converts matter at the atomic level, turning scrap into useful materials.',
    type: 'artifact',
    effectDescription: 'Generates 1 unit of fuel and materials per exploration turn.',
    powerDraw: 4,
    mass: 3,
    bonus: { stat: 'resource.generation', value: 1, operator: 'add' }
  },
  {
    id: 'art_utility_2',
    name: 'Probability Calculator',
    description: 'Quantum computer that calculates the most favorable outcomes.',
    type: 'artifact',
    effectDescription: 'Increases success chance of all random events by 15%.',
    powerDraw: 3,
    mass: 2,
    bonus: { stat: 'luck.modifier', value: 15, operator: 'add' }
  },
  {
    id: 'art_utility_3',
    name: 'Crew Enhancement Pod',
    description: 'Biotechnology that enhances crew capabilities and extends lifespans.',
    type: 'artifact',
    effectDescription: 'All crew gain +1 skill level and immunity to space hazards.',
    powerDraw: 7,
    mass: 12,
    bonus: { stat: 'crew.skill', value: 1, operator: 'add' }
  },
  {
    id: 'art_utility_4',
    name: 'Temporal Stabilizer',
    description: 'Maintains consistent time flow, preventing temporal anomalies.',
    type: 'artifact',
    effectDescription: 'Immune to time-based attacks and gains extra actions in combat.',
    powerDraw: 10,
    mass: 6,
    bonus: { stat: 'temporal.resistance', value: 100, operator: 'add' }
  },
  {
    id: 'art_utility_5',
    name: 'Architect Data Core',
    description: 'Contains vast knowledge of the ancient civilization and their technologies.',
    type: 'artifact',
    effectDescription: 'Unlocks advanced technology research and reveals artifact locations.',
    powerDraw: 8,
    mass: 4,
    bonus: { stat: 'research.speed', value: 200, operator: 'add' }
  }
];

// ===========================================
// EXPANDED EQUIPMENT (4 of each type)
// ===========================================

// Additional Weapons
export const EXPANDED_WEAPONS = [
  {
    id: 'w_plasma_cannon',
    name: 'Plasma Cannon',
    description: 'Fires superheated plasma bolts that can melt through most armor.',
    type: 'weapon',
    damage: 25,
    range: 4,
    powerDraw: 18,
    mass: 8,
  },
  {
    id: 'w_rail_gun',
    name: 'Electromagnetic Rail Gun',
    description: 'Accelerates metal projectiles to incredible velocities using magnetic fields.',
    type: 'weapon',
    damage: 35,
    range: 7,
    powerDraw: 25,
    mass: 12,
  },
  {
    id: 'w_quantum_disruptor',
    name: 'Quantum Disruptor',
    description: 'Destabilizes molecular bonds at the quantum level. Highly effective against shields.',
    type: 'weapon',
    damage: 18,
    range: 6,
    powerDraw: 22,
    mass: 7,
    special: 'Ignores 50% of shield protection'
  },
  {
    id: 'w_void_lance',
    name: 'Void Lance',
    description: 'Channels energy from subspace into a devastating beam weapon.',
    type: 'weapon',
    damage: 45,
    range: 8,
    powerDraw: 35,
    mass: 15,
    special: 'Damage increases with distance'
  }
];

// Additional Engines
export const EXPANDED_ENGINES = [
  {
    id: 'e_antimatter_drive',
    name: 'Antimatter Drive',
    description: 'High-performance engine using matter-antimatter annihilation for propulsion.',
    type: 'engine',
    thrust: 6,
    fuelConsumption: 15,
    powerDraw: 30,
    mass: 25,
  },
  {
    id: 'e_quantum_drive',
    name: 'Quantum Tunnel Drive',
    description: 'Manipulates quantum foam to achieve near-instantaneous acceleration.',
    type: 'engine',
    thrust: 8,
    fuelConsumption: 8,
    powerDraw: 40,
    mass: 18,
  },
  {
    id: 'e_warp_core',
    name: 'Spatial Warp Core',
    description: 'Bends space-time around the vessel for incredibly efficient travel.',
    type: 'engine',
    thrust: 5,
    fuelConsumption: 20,
    powerDraw: 35,
    mass: 30,
    special: 'Can bypass some spatial hazards'
  },
  {
    id: 'e_stellar_ram',
    name: 'Stellar Ramjet',
    description: 'Collects interstellar hydrogen and fuses it for propulsion.',
    type: 'engine',
    thrust: 7,
    fuelConsumption: 25,
    powerDraw: 20,
    mass: 22,
    special: 'Generates fuel when near stars'
  }
];

// Additional Shields
export const EXPANDED_SHIELDS = [
  {
    id: 's_energy_barrier',
    name: 'Energy Barrier Generator',
    description: 'Creates a dense energy field that absorbs and disperses incoming attacks.',
    type: 'shield',
    capacity: 150,
    rechargeRate: 15,
    powerDraw: 20,
    mass: 12,
  },
  {
    id: 's_phase_shield',
    name: 'Phase Shield Array',
    description: 'Shifts the ship partially out of phase, causing attacks to pass through harmlessly.',
    type: 'shield',
    capacity: 120,
    rechargeRate: 20,
    powerDraw: 25,
    mass: 8,
    special: '25% chance to completely avoid attacks'
  },
  {
    id: 's_adaptive_shield',
    name: 'Adaptive Defense Grid',
    description: 'Learns from incoming damage and adapts to provide better protection.',
    type: 'shield',
    capacity: 200,
    rechargeRate: 12,
    powerDraw: 30,
    mass: 15,
    special: 'Gains resistance to repeated damage types'
  },
  {
    id: 's_regenerative_hull',
    name: 'Regenerative Hull Plating',
    description: 'Self-repairing armor that can seal breaches and restore structural integrity.',
    type: 'shield',
    capacity: 180,
    rechargeRate: 8,
    powerDraw: 18,
    mass: 20,
    special: 'Also repairs hull damage over time'
  }
];

// ===========================================
// SHIP HULLS (Multiple size classes)
// ===========================================

export const SHIP_HULLS = [
  {
    id: 'hull_scout',
    name: 'Scout Frigate',
    description: 'Fast, nimble vessel designed for reconnaissance and long-range exploration.',
    class: 'Light',
    hull: 80,
    maxPower: 120,
    cargoSpace: 15,
    crewCapacity: 3,
    weaponSlots: 2,
    mass: 40,
    bonus: '+2 movement range, +25% fuel efficiency'
  },
  {
    id: 'hull_interceptor',
    name: 'Interceptor',
    description: 'Military light fighter optimized for speed and maneuverability.',
    class: 'Light',
    hull: 60,
    maxPower: 100,
    cargoSpace: 8,
    crewCapacity: 2,
    weaponSlots: 3,
    mass: 30,
    bonus: '+3 movement range, +15% weapon accuracy'
  },
  {
    id: 'hull_cruiser',
    name: 'Heavy Cruiser',
    description: 'Well-balanced warship with good firepower and defensive capabilities.',
    class: 'Medium',
    hull: 150,
    maxPower: 200,
    cargoSpace: 25,
    crewCapacity: 5,
    weaponSlots: 4,
    mass: 80,
    bonus: '+25% shield capacity, +1 weapon slot'
  },
  {
    id: 'hull_explorer',
    name: 'Deep Space Explorer',
    description: 'Long-range vessel equipped for extended missions in uncharted space.',
    class: 'Medium',
    hull: 120,
    maxPower: 180,
    cargoSpace: 40,
    crewCapacity: 6,
    weaponSlots: 2,
    mass: 70,
    bonus: '+50% cargo space, enhanced sensor range'
  },
  {
    id: 'hull_battleship',
    name: 'Battleship',
    description: 'Massive warship bristling with weapons and heavy armor.',
    class: 'Heavy',
    hull: 250,
    maxPower: 300,
    cargoSpace: 30,
    crewCapacity: 8,
    weaponSlots: 6,
    mass: 150,
    bonus: '+50% hull strength, can mount heavy weapons'
  },
  {
    id: 'hull_carrier',
    name: 'Fleet Carrier',
    description: 'Massive vessel that serves as a mobile base of operations.',
    class: 'Heavy',
    hull: 300,
    maxPower: 350,
    cargoSpace: 60,
    crewCapacity: 12,
    weaponSlots: 4,
    mass: 200,
    bonus: 'Can deploy fighter squadrons, massive cargo capacity'
  },
  {
    id: 'hull_dreadnought',
    name: 'Dreadnought',
    description: 'The ultimate expression of military might - a ship-killer of legendary proportions.',
    class: 'Super-Heavy',
    hull: 400,
    maxPower: 450,
    cargoSpace: 35,
    crewCapacity: 10,
    weaponSlots: 8,
    mass: 250,
    bonus: 'Can mount super-heavy weapons, intimidates enemy ships'
  }
];

// ===========================================
// CREW EXPANSION
// ===========================================

export const EXPANDED_CREW = [
  {
    id: 'crew_4',
    name: 'Zara "Ghostwire" Chen',
    role: 'Communications',
    skillLevel: 3,
    description: 'Former intelligence operative who can intercept and decode any transmission.',
    bonusEffect: 'Reveals enemy positions and intentions in tactical situations.'
  },
  {
    id: 'crew_5',
    name: 'Commander Thex Kravik',
    role: 'Tactical',
    skillLevel: 4,
    description: 'Veteran of a hundred space battles, master of combat strategy and ship maneuvering.',
    bonusEffect: '+1 to all combat actions and can predict enemy movements.'
  },
  {
    id: 'crew_6',
    name: 'Dr. Lyra Synthesis',
    role: 'Science',
    skillLevel: 4,
    description: 'Xenobiologist and archaeologist specializing in ancient technologies.',
    bonusEffect: 'Can analyze artifacts and alien technology for additional bonuses.'
  },
  {
    id: 'crew_7',
    name: 'Kai "Miracle" Rodriguez',
    role: 'Medic',
    skillLevel: 2,
    description: 'Ship\'s doctor who has saved more lives than can be counted.',
    bonusEffect: 'Crew members recover from injuries 50% faster.'
  },
  {
    id: 'crew_8',
    name: 'Echo-Seven',
    role: 'AI Specialist',
    skillLevel: 5,
    description: 'Uploaded consciousness of an ancient AI researcher, exists in the ship\'s computers.',
    bonusEffect: 'All ship systems operate 25% more efficiently.'
  }
];

// ===========================================
// PROGRESSION SYSTEM
// ===========================================

export const PROGRESSION_SYSTEM = {
  researchTrees: [
    {
      name: "Ancient Technology",
      description: "Unlock the secrets of the Architects",
      requirements: "Find Architect ruins and artifacts",
      unlocks: ["Advanced artifacts", "Nexus Fragment locations", "Stellar Aegis components"]
    },
    {
      name: "Void Storm Research", 
      description: "Study the approaching cosmic threat",
      requirements: "Encounter Void Storm phenomena",
      unlocks: ["Storm-resistant hulls", "Void weapons", "Temporal shields"]
    },
    {
      name: "Galactic Diplomacy",
      description: "Unite the species against the common threat",
      requirements: "Establish contact with all major species",
      unlocks: ["Alliance fleets", "Combined technologies", "Coordinated defense"]
    }
  ],
  
  endgameProgression: [
    {
      phase: 1,
      title: "The Awakening",
      description: "Discover the Void Storm threat and begin the search for answers",
      objectives: ["Detect first Void Storm phenomena", "Meet 3 different alien species", "Find first Architect ruin"]
    },
    {
      phase: 2,
      title: "The Hunt",
      description: "Actively search for Nexus Fragments across the galaxy",
      objectives: ["Collect 3 Nexus Fragments", "Decode Architect star charts", "Build alliance with 2 species"]
    },
    {
      phase: 3,
      title: "The Convergence", 
      description: "Unite the galaxy and prepare for the final confrontation",
      objectives: ["Collect all 5 Nexus Fragments", "Form galactic alliance", "Reach the Nexus Chamber"]
    },
    {
      phase: 4,
      title: "The Aegis Protocol",
      description: "Activate the Stellar Aegis and face the Void Storm",
      objectives: ["Survive Void Storm entities", "Complete the Aegis sequence", "Save the galaxy"]
    }
  ]
};


3. The MerchGenie shop needs to be implemented. 