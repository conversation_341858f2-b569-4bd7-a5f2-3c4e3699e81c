'use client';

import Login from '@/components/auth/Login';
import CryptoWalletLogin from '@/components/auth/CryptoWalletLogin';
import { Suspense, useState, useEffect } from 'react';
import { Rocket, Wallet, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AuthStorage } from '@/lib/auth-storage';

export default function HomePage() {
  const [loginMethod, setLoginMethod] = useState<'standard' | 'wallet'>('wallet');

  useEffect(() => {
    // Clear any invalid tokens on app startup to prevent errors
    try {
      AuthStorage.getToken(); // This will clear invalid tokens automatically
    } catch (error) {
      console.error('Error checking tokens on startup:', error);
      AuthStorage.clearTokens();
    }
  }, []);

  return (
    <main className="h-screen w-screen bg-background font-body text-foreground overflow-hidden flex items-center justify-center">
      <div className="text-center p-4 max-w-lg mx-auto">
        <div className="flex justify-center items-center gap-4 mb-4">
            <Rocket className="h-12 w-12 text-primary" />
            <h1 className="text-5xl font-headline text-primary">Starship Voyager</h1>
        </div>
        <p className="mb-8 text-muted-foreground max-w-md mx-auto">
          Your turn-based space exploration and combat adventure awaits. Choose your login method to begin your journey across the stars.
        </p>

        {/* Login Method Selection */}
        <div className="mb-6">
          <Card className="p-2">
            <CardContent className="flex gap-2 p-2">
              <Button
                variant={loginMethod === 'standard' ? 'default' : 'outline'}
                onClick={() => setLoginMethod('standard')}
                className="flex-1"
              >
                <Mail className="mr-2 h-4 w-4" />
                Standard Login
              </Button>
              <Button
                variant={loginMethod === 'wallet' ? 'default' : 'outline'}
                onClick={() => setLoginMethod('wallet')}
                className="flex-1"
              >
                <Wallet className="mr-2 h-4 w-4" />
                Crypto Wallet
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Login Component */}
        {loginMethod === 'wallet' ? (
          <CryptoWalletLogin />
        ) : (
          <Suspense fallback={<div className="text-muted-foreground">Loading Login...</div>}>
            <Login />
          </Suspense>
        )}
      </div>
    </main>
  );
}
