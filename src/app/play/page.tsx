'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import GameContainer from '@/components/GameContainer';
import { AuthStorage } from '@/lib/auth-storage';
import { AuthLoadingState } from '@/components/LoadingState';
import { isWalletAuthenticated } from '@/lib/crypto-auth';

export default function PlayPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // This check runs on the client-side using secure token storage
    try {
      // Check both standard auth and crypto wallet auth
      if (!AuthStorage.isAuthenticated() && !isWalletAuthenticated()) {
        router.push('/');
      } else {
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Authentication check error:', error);
      // Clear any invalid tokens and redirect to login
      AuthStorage.clearTokens();
      router.push('/');
    }
    setIsLoading(false);
  }, [router]);

  if (isLoading) {
    return <AuthLoadingState />;
  }
  
  if (!isAuthenticated) {
    // Render nothing while redirecting
    return null;
  }

  return (
    <main className="h-screen w-screen bg-background font-body text-foreground overflow-hidden">
      <GameContainer />
    </main>
  );
}
