'use client';

import React from 'react';

declare global {
  interface Window {
    Bedrock: {
      BedrockPassportProvider: React.ComponentType<any>;
      LoginPanel: React.ComponentType<any>;
      useBedrockPassport: () => any;
    };
  }
}

export function BedrockPassportProviderWrapper({ children }: { children: React.ReactNode }) {
  const [BedrockPassportProvider, setBedrockPassportProvider] = React.useState<React.ComponentType<any> | null>(null);

  React.useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const checkBedrock = () => {
        if (window.Bedrock && window.Bedrock.BedrockPassportProvider) {
          setBedrockPassportProvider(() => window.Bedrock.BedrockPassportProvider);
        } else {
          // Retry after a short delay if Bedrock isn't loaded yet
          setTimeout(checkBedrock, 100);
        }
      };
      checkBedrock();
    }
  }, []);

  // During SSR, just return children without the provider
  if (typeof window === 'undefined') {
    return <>{children}</>;
  }

  if (!BedrockPassportProvider) {
    return <>{children}</>; // Return children while loading
  }

  const bedrockConfig = {
    baseUrl: 'https://api.bedrockpassport.com',
    authCallbackUrl: 'https://orangequest.merchgenieai.com/auth/callback',
    tenantId: process.env.NEXT_PUBLIC_ORANGE_ID_PROJECT_ID,
    subscriptionKey: process.env.NEXT_PUBLIC_ORANGE_ID_API_KEY,
  };

  try {
    return React.createElement(BedrockPassportProvider, bedrockConfig, children);
  } catch (error) {
    console.error('Error creating Bedrock provider:', error);
    return <>{children}</>;
  }
}
