'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { LoadingState, GameLoadingState, BattleLoadingState, SystemLoadingState } from './LoadingState';

/**
 * Dynamically imported components for code splitting
 * This reduces the initial bundle size by loading components only when needed
 */

// Game screens with appropriate loading states
export const DynamicCharacterCreationScreen = dynamic(
  () => import('./screens/CharacterCreationScreen').then(mod => ({ default: mod.default })),
  {
    loading: () => <GameLoadingState message="LOADING CHARACTER CREATION..." />,
    ssr: false
  }
);

export const DynamicStarmapScreen = dynamic(
  () => import('./screens/StarmapScreen'),
  {
    loading: () => <GameLoadingState message="LOADING STARMAP..." />,
    ssr: false
  }
);

export const DynamicBattleScreen = dynamic(
  () => import('./screens/BattleScreen'),
  {
    loading: () => <BattleLoadingState />,
    ssr: false
  }
);

export const DynamicPlanetScreen = dynamic(
  () => import('./screens/PlanetScreen'),
  {
    loading: () => <SystemLoadingState message="LOADING PLANET..." />,
    ssr: false
  }
);

export const DynamicShipManagementScreen = dynamic(
  () => import('./screens/ShipManagementScreen'),
  {
    loading: () => <GameLoadingState message="LOADING SHIP MANAGEMENT..." />,
    ssr: false
  }
);

export const DynamicSystemViewScreen = dynamic(
  () => import('./screens/SystemViewScreen'),
  {
    loading: () => <SystemLoadingState />,
    ssr: false
  }
);

// Heavy components that can be loaded on demand
export const DynamicGameCanvas = dynamic(
  () => import('./GameCanvas'),
  {
    loading: () => <LoadingState message="Loading starmap..." variant="minimal" />,
    ssr: false
  }
);

// Auth components
export const DynamicLogin = dynamic(
  () => import('./auth/Login'),
  {
    loading: () => <LoadingState message="Loading authentication..." />,
    ssr: false
  }
);

/**
 * Preload components for better UX
 * Call these functions to preload components before they're needed
 */
export const preloadGameScreens = () => {
  // Preload commonly used screens
  import('./screens/StarmapScreen');
  import('./screens/BattleScreen');
  import('./GameCanvas');
};

export const preloadAllScreens = () => {
  // Preload all game screens
  import('./screens/CharacterCreationScreen');
  import('./screens/StarmapScreen');
  import('./screens/BattleScreen');
  import('./screens/PlanetScreen');
  import('./screens/ShipManagementScreen');
  import('./screens/SystemViewScreen');
  import('./GameCanvas');
};

/**
 * Hook to preload components based on game state
 */
export const usePreloadComponents = (currentState: string) => {
  React.useEffect(() => {
    switch (currentState) {
      case 'character-creation':
        // Preload starmap for next likely screen
        import('./screens/StarmapScreen');
        import('./GameCanvas');
        break;
      case 'starmap':
        // Preload battle and system view screens
        import('./screens/BattleScreen');
        import('./screens/SystemViewScreen');
        break;
      case 'system-view':
        // Preload planet and battle screens
        import('./screens/PlanetScreen');
        import('./screens/BattleScreen');
        break;
      case 'battle':
        // Preload ship management for post-battle
        import('./screens/ShipManagementScreen');
        break;
    }
  }, [currentState]);
};


