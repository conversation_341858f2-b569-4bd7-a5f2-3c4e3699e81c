'use client';

import React from 'react';
import { Rocket, Loader2, Zap, Shield, Fuel } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  message?: string;
  variant?: 'default' | 'game' | 'minimal';
  className?: string;
}

/**
 * Reusable loading state component with different variants
 */
export function LoadingState({ 
  message = 'Loading...', 
  variant = 'default',
  className 
}: LoadingStateProps) {
  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center justify-center p-4", className)}>
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2 text-sm text-muted-foreground">{message}</span>
      </div>
    );
  }

  if (variant === 'game') {
    return (
      <div className={cn("flex h-full w-full items-center justify-center flex-col gap-4", className)}>
        <div className="relative">
          <Rocket className="h-16 w-16 animate-pulse text-primary" />
          <div className="absolute -top-2 -right-2 animate-spin">
            <Zap className="h-6 w-6 text-yellow-400" />
          </div>
        </div>
        <p className="text-xl font-headline tracking-widest text-center">{message}</p>
        <div className="flex gap-2 mt-4">
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex h-full w-full items-center justify-center flex-col gap-6 p-8", className)}>
      <div className="relative">
        <div className="absolute inset-0 animate-ping">
          <Rocket className="h-12 w-12 text-primary/30" />
        </div>
        <Rocket className="h-12 w-12 text-primary animate-pulse" />
      </div>
      <div className="text-center space-y-2">
        <p className="text-lg font-headline tracking-wider">{message}</p>
        <div className="flex justify-center gap-1">
          <div className="w-1 h-1 bg-primary rounded-full animate-pulse" />
          <div className="w-1 h-1 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
          <div className="w-1 h-1 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
        </div>
      </div>
    </div>
  );
}

/**
 * Game-specific loading states
 */
export function GameLoadingState({ message = 'LOADING ORANGE ALERT...' }: { message?: string }) {
  return <LoadingState message={message} variant="game" />;
}

export function AuthLoadingState({ message = 'VERIFYING FLIGHT-PLAN...' }: { message?: string }) {
  return <LoadingState message={message} variant="game" />;
}

export function BattleLoadingState({ message = 'PREPARING FOR COMBAT...' }: { message?: string }) {
  return (
    <div className="flex h-full w-full items-center justify-center flex-col gap-4">
      <div className="relative">
        <Shield className="h-16 w-16 animate-pulse text-blue-400" />
        <div className="absolute -top-2 -right-2 animate-bounce">
          <Zap className="h-6 w-6 text-red-400" />
        </div>
      </div>
      <p className="text-xl font-headline tracking-widest text-center">{message}</p>
      <div className="flex gap-2 mt-4">
        <div className="w-2 h-2 bg-red-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
    </div>
  );
}

export function SystemLoadingState({ message = 'SCANNING SYSTEM...' }: { message?: string }) {
  return (
    <div className="flex h-full w-full items-center justify-center flex-col gap-4">
      <div className="relative">
        <div className="absolute inset-0 animate-spin">
          <div className="w-16 h-16 border-2 border-primary border-t-transparent rounded-full" />
        </div>
        <Fuel className="h-8 w-8 text-primary absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
      </div>
      <p className="text-xl font-headline tracking-widest text-center">{message}</p>
    </div>
  );
}

/**
 * Skeleton loading components for specific UI elements
 */
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("animate-pulse", className)}>
      <div className="bg-muted rounded-lg p-4 space-y-3">
        <div className="h-4 bg-muted-foreground/20 rounded w-3/4" />
        <div className="space-y-2">
          <div className="h-3 bg-muted-foreground/20 rounded" />
          <div className="h-3 bg-muted-foreground/20 rounded w-5/6" />
        </div>
      </div>
    </div>
  );
}

export function SkeletonList({ count = 3, className }: { count?: number; className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="animate-pulse">
          <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
            <div className="w-8 h-8 bg-muted-foreground/20 rounded-full" />
            <div className="flex-1 space-y-1">
              <div className="h-3 bg-muted-foreground/20 rounded w-1/2" />
              <div className="h-2 bg-muted-foreground/20 rounded w-3/4" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Hook for managing loading states
 */
export function useLoadingState(initialState = false) {
  const [isLoading, setIsLoading] = React.useState(initialState);
  const [loadingMessage, setLoadingMessage] = React.useState<string>('Loading...');

  const startLoading = React.useCallback((message?: string) => {
    if (message) setLoadingMessage(message);
    setIsLoading(true);
  }, []);

  const stopLoading = React.useCallback(() => {
    setIsLoading(false);
  }, []);

  const withLoading = React.useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    startLoading(message);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      stopLoading();
    }
  }, [startLoading, stopLoading]);

  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    withLoading
  };
}
