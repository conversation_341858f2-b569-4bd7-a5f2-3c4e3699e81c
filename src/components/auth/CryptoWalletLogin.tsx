'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Wallet, Shield, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { 
  isWalletAvailable, 
  authenticateWithWallet, 
  getCurrentWalletAddress,
  onWalletAccountChange,
  isAuthorizedWallet
} from '@/lib/crypto-auth';

interface CryptoWalletLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export default function CryptoWalletLogin({ onSuccess, onError }: CryptoWalletLoginProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAddress, setCurrentAddress] = useState<string | null>(null);
  const [walletAvailable, setWalletAvailable] = useState(false);

  useEffect(() => {
    // Check if wallet is available
    setWalletAvailable(isWalletAvailable());

    // Get current address if already connected
    getCurrentWalletAddress().then(address => {
      setCurrentAddress(address);
    });

    // Listen for account changes
    const cleanup = onWalletAccountChange((accounts) => {
      setCurrentAddress(accounts.length > 0 ? accounts[0] : null);
      if (accounts.length === 0) {
        setError('Wallet disconnected');
      }
    });

    return cleanup;
  }, []);

  const handleWalletLogin = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await authenticateWithWallet();
      
      if (result.success) {
        if (onSuccess) {
          onSuccess();
        } else {
          router.push('/play');
        }
      } else {
        const errorMessage = result.error || 'Authentication failed';
        setError(errorMessage);
        if (onError) {
          onError(errorMessage);
        }
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isAddressAuthorized = currentAddress ? isAuthorizedWallet(currentAddress) : false;

  if (!walletAvailable) {
    return (
      <Card className="w-full max-w-md border-destructive/20">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-destructive">Wallet Not Found</CardTitle>
          <CardDescription>
            No crypto wallet detected. Please install MetaMask or a compatible wallet to continue.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => window.open('https://metamask.io/', '_blank')} 
            className="w-full"
            variant="outline"
          >
            Install MetaMask
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md border-primary/20 shadow-lg shadow-primary/10">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <Wallet className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-primary font-headline">Crypto Wallet Login</CardTitle>
        <CardDescription>
          Connect your authorized crypto wallet to access Orange Alert
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {currentAddress && (
          <div className="p-3 rounded-lg bg-muted/50 border border-muted">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Connected Wallet</span>
            </div>
            <p className="text-xs font-mono text-muted-foreground break-all">
              {currentAddress}
            </p>
            <div className="flex items-center gap-2 mt-2">
              {isAddressAuthorized ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-500">Authorized</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                  <span className="text-sm text-destructive">Not Authorized</span>
                </>
              )}
            </div>
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-3">
          <Button
            onClick={handleWalletLogin}
            disabled={isLoading || (!!currentAddress && !isAddressAuthorized)}
            className="w-full font-headline tracking-wider"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Authenticating...
              </>
            ) : (
              <>
                <Wallet className="mr-2 h-4 w-4" />
                {currentAddress ? 'Sign & Enter Game' : 'Connect Wallet'}
              </>
            )}
          </Button>

          {!currentAddress && (
            <p className="text-xs text-muted-foreground text-center">
              Click to connect your wallet and sign an authentication message
            </p>
          )}
        </div>

        <div className="pt-4 border-t border-muted">
          <div className="text-xs text-muted-foreground space-y-1">
            <p className="font-medium">Authorized Wallet:</p>
            <p className="font-mono break-all">0xF2F4...8b02</p>
            <p className="text-center mt-2">Only this wallet can access the game</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
