'use client';

import React from 'react';
import ReactDOM from 'react-dom/client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthStorage } from '@/lib/auth-storage';
import { LoadingState } from '@/components/LoadingState';

declare global {
  interface Window {
    React: typeof React;
    ReactDOM: any;
    Bedrock: {
      BedrockPassportProvider: React.ComponentType<any>;
      LoginPanel: React.ComponentType<any>;
      useBedrockPassport: () => any;
    };
  }
}

export default function Login() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBedrockLibrary = async () => {
      try {
        // Make React available globally for Bedrock
        if (typeof window !== 'undefined') {
          window.React = React;
          window.ReactDOM = ReactDOM;
        }

        // Check if Bedrock is already loaded
        if (window.Bedrock) {
          setIsLoading(false);
          initializeBedrock();
          return;
        }

        // Dynamically load the Bedrock script
        const script = document.createElement('script');
        script.src = 'https://public-cdn-files.pages.dev/bedrock-passport.umd.js';
        script.async = true;

        script.onload = () => {
          // Wait a bit for the library to initialize
          setTimeout(() => {
            if (window.Bedrock) {
              setIsLoading(false);
              initializeBedrock();
            } else {
              setError('Failed to initialize Bedrock library');
              setIsLoading(false);
            }
          }, 100);
        };

        script.onerror = () => {
          setError('Failed to load authentication library');
          setIsLoading(false);
        };

        document.head.appendChild(script);

        // Cleanup function
        return () => {
          if (script.parentNode) {
            script.parentNode.removeChild(script);
          }
        };
      } catch (error) {
        console.error('Error loading Bedrock:', error);
        setError('Failed to load authentication library');
        setIsLoading(false);
      }
    };

    loadBedrockLibrary();
  }, []);

  const initializeBedrock = () => {
    // Ensure Bedrock library is loaded
    if (!window.Bedrock) {
      setError('Authentication library not available');
      return;
    }

    const bedrockConfig = {
      baseUrl: 'https://api.bedrockpassport.com',
      authCallbackUrl: window.location.origin,
      tenantId: process.env.NEXT_PUBLIC_ORANGE_ID_PROJECT_ID,
      subscriptionKey: process.env.NEXT_PUBLIC_ORANGE_ID_API_KEY,
    };

    const container = document.getElementById('bedrock-login-widget');
    if (!container) {
      console.error('Bedrock login widget container not found.');
      return;
    }

    const root = ReactDOM.createRoot(container);
    const params = new URLSearchParams(window.location.search);
    const token = params.get('token');
    const refreshToken = params.get('refreshToken');

    if (token && refreshToken) {
      // We're handling a callback
      function AuthCallbackProcessor() {
        const { loginCallback } = window.Bedrock.useBedrockPassport();
        const [message, setMessage] = React.useState('Processing authentication...');

        React.useEffect(() => {
          async function processLogin() {
            try {
              setMessage('Verifying tokens...');
              const success = await loginCallback(token, refreshToken);

              if (success && token) {
                // Store tokens securely
                const tokenStored = AuthStorage.setTokens({
                  token: token,
                  refreshToken: refreshToken || undefined,
                  expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours from now
                });

                if (tokenStored) {
                  setMessage('Login successful! Redirecting...');
                  window.history.replaceState({}, document.title, window.location.pathname);
                  router.push('/play'); // Redirect to the game
                } else {
                  setMessage('Error storing authentication data. Please try again.');
                }
              } else {
                setMessage('Authentication failed. Please try again.');
              }
            } catch (error) {
              console.error('Login error:', error);
              setMessage('An error occurred during login. Please try again.');
            }
          }
          processLogin();
        }, [loginCallback]);

        return React.createElement('div', null, message);
      }

      root.render(
        React.createElement(
          window.Bedrock.BedrockPassportProvider,
          bedrockConfig,
          React.createElement(AuthCallbackProcessor)
        )
      );
    } else {
      // Normal login flow
      root.render(
        React.createElement(
          window.Bedrock.BedrockPassportProvider,
          bedrockConfig,
          React.createElement(window.Bedrock.LoginPanel, {
            title: 'Sign in to',
            logo: 'https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg',
            logoAlt: 'Orange Web3',
            walletButtonText: 'Connect Wallet',
            showConnectWallet: false,
            separatorText: 'OR',
            features: {
              enableWalletConnect: false,
              enableAppleLogin: true,
              enableGoogleLogin: true,
              enableEmailLogin: false,
            },
            titleClass: 'text-xl font-bold',
            logoClass: 'ml-2 md:h-8 h-6',
            panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
            buttonClass: 'hover:border-orange-500',
            separatorTextClass: 'bg-orange-900 text-gray-500',
            separatorClass: 'bg-orange-900',
            linkRowClass: 'justify-center',
            headerClass: 'justify-center',
          })
        )
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingState message="Loading authentication..." variant="minimal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div id="bedrock-login-widget"></div>
  );
}
