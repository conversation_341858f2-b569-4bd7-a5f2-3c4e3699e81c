"use client";

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { RACES } from '@/lib/constants';
import type { Player, Race } from '@/types';
import { validationSchemas, validatePlayerName, validateShipName, validateRace } from '@/lib/input-validation';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from '@/components/ui/label';
import { User, Rocket } from 'lucide-react';

const formSchema = z.object({
  name: validationSchemas.playerName,
  shipName: validationSchemas.shipName,
  race: z.enum(RACES.map(r => r.id) as [string, ...string[]], {
    required_error: "You need to select a race.",
  }),
});

type CharacterCreationProps = {
  onComplete: (player: Player) => void;
};

export default function CharacterCreationScreen({ onComplete }: CharacterCreationProps) {
  const [selectedRace, setSelectedRace] = useState<Race | null>(RACES[0]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: "", shipName: "", race: 'human' },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // Additional validation before submission
    const nameValidation = validatePlayerName(values.name);
    const shipNameValidation = validateShipName(values.shipName);
    const raceValidation = validateRace(values.race, RACES.map(r => r.id));

    if (!nameValidation.isValid || !shipNameValidation.isValid || !raceValidation.isValid) {
      console.error('Validation failed:', {
        name: nameValidation.error,
        shipName: shipNameValidation.error,
        race: raceValidation.error
      });
      return;
    }

    const raceData = RACES.find(r => r.id === values.race);
    if (raceData) {
      onComplete({
        name: nameValidation.sanitized,
        shipName: shipNameValidation.sanitized,
        race: raceData,
      });
    }
  };

  const handleRaceChange = (raceId: string) => {
    setSelectedRace(RACES.find(r => r.id === raceId) || null);
    form.setValue('race', raceId as 'human' | 'alien' | 'robot');
  };

  return (
    <div className="flex h-full w-full items-center justify-center bg-background p-8">
      <Card className="w-full max-w-2xl border-primary/20 shadow-lg shadow-primary/10">
        <CardHeader>
          <CardTitle className="font-headline text-4xl text-primary tracking-wider">Create Your Captain</CardTitle>
          <CardDescription>Begin your journey across the stars. Who will you be?</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2"><User className="h-4 w-4" /> Captain's Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Jax Star-Rider" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="shipName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2"><Rocket className="h-4 w-4" /> Ship's Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., The Comet's Tail" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="race"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="text-lg font-headline">Choose Your Origin</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={handleRaceChange}
                        defaultValue={field.value}
                        className="grid grid-cols-1 md:grid-cols-3 gap-4"
                      >
                        {RACES.map(race => (
                          <FormItem key={race.id}>
                            <Label htmlFor={race.id} className={`flex flex-col items-center justify-center rounded-md border-2 p-4 hover:border-accent cursor-pointer transition-colors ${field.value === race.id ? 'border-primary' : 'border-muted'}`}>
                               <FormControl>
                                <RadioGroupItem value={race.id} id={race.id} className="sr-only" />
                               </FormControl>
                              <p className="font-bold text-lg">{race.name}</p>
                            </Label>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedRace && (
                  <div className="p-4 rounded-lg bg-muted/50 border border-muted">
                      <h3 className="font-bold text-primary font-headline">{selectedRace.name}</h3>
                      <p className="text-sm text-muted-foreground">{selectedRace.description}</p>
                      <p className="text-sm mt-2"><strong className="text-foreground">Bonus:</strong> {selectedRace.bonus}</p>
                  </div>
              )}

            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full font-headline tracking-wider text-lg" size="lg">Embark on Your Journey</Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}
