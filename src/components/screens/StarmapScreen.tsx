"use client";

import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import type { GameData, GameState, StarSystem } from '@/types';
import GameCanvas from '../GameCanvas';
import Hud from '../Hud';
import { Button } from '../ui/button';
import type { Dispatch, SetStateAction } from 'react';
import { useToast } from '@/hooks/use-toast';

const TRAVEL_SPEED = 2; // In "original" pixels per frame

type StarmapScreenProps = {
  gameData: GameData;
  setGameData: Dispatch<SetStateAction<GameData>>;
  setGameState: (state: GameState) => void;
  handleQuit: () => void;
  isPausedByPlatform: boolean;
};

export default function StarmapScreen({ gameData, setGameData, setGameState, handleQuit, isPausedByPlatform }: StarmapScreenProps) {
  const { toast } = useToast();
  const [isPaused, setIsPaused] = useState(false);
  const [isTraveling, setIsTraveling] = useState(false);
  const [shipDisplayPosition, setShipDisplayPosition] = useState<{ x: number; y: number } | null>(null);

  const animationFrameRef = useRef<number>();
  
  const gameIsPaused = isPaused || isPausedByPlatform;

  const currentSystem = useMemo(() => 
    gameData.starmap.systems.find(p => p.id === gameData.currentSystemId)
  , [gameData.starmap, gameData.currentSystemId]);
  
  const fuelConsumptionRate = useMemo(() => gameData.ship.engine.fuelConsumption, [gameData.ship.engine]);
  const travelRange = useMemo(() => gameData.ship.fuel.current * fuelConsumptionRate, [gameData.ship.fuel.current, fuelConsumptionRate]);

  // Set initial ship position or update it when not traveling
  useEffect(() => {
    if (currentSystem && !isTraveling) {
      // Position ship beside the system instead of inside it
      const offset = currentSystem.size + 20; // Distance from system edge
      const angle = Math.PI / 4; // 45 degrees - position ship to the bottom-right
      const shipX = currentSystem.x + Math.cos(angle) * offset;
      const shipY = currentSystem.y + Math.sin(angle) * offset;
      setShipDisplayPosition({ x: shipX, y: shipY });
    }
  }, [currentSystem, isTraveling]);

  const handleTravel = useCallback((targetSystem: StarSystem) => {
    if (!currentSystem || isTraveling || gameIsPaused) return;

    const distance = Math.hypot(targetSystem.x - currentSystem.x, targetSystem.y - currentSystem.y);
    const fuelCost = Math.ceil(distance / fuelConsumptionRate);

    if (gameData.ship.fuel.current < fuelCost) {
      toast({
        variant: 'destructive',
        title: 'Not enough fuel',
        description: `You need ${fuelCost} fuel, but you only have ${gameData.ship.fuel.current}.`,
      });
      return;
    }

    setIsTraveling(true);

    // Start travel animation
    const startPos = shipDisplayPosition || { x: currentSystem.x, y: currentSystem.y };
    // Position target beside the destination system
    const targetOffset = targetSystem.size + 20;
    const targetAngle = Math.PI / 4; // 45 degrees
    const endPos = {
      x: targetSystem.x + Math.cos(targetAngle) * targetOffset,
      y: targetSystem.y + Math.sin(targetAngle) * targetOffset
    };
    const totalDistance = Math.hypot(endPos.x - startPos.x, endPos.y - startPos.y);
    const totalFrames = totalDistance / TRAVEL_SPEED;
    let frame = 0;

    const animate = () => {
      frame++;
      const progress = Math.min(frame / totalFrames, 1);
      const newX = startPos.x + (endPos.x - startPos.x) * progress;
      const newY = startPos.y + (endPos.y - startPos.y) * progress;
      
      setShipDisplayPosition({ x: newX, y: newY });

      if (progress < 1) {
        animationFrameRef.current = requestAnimationFrame(animate);
      } else {
        // Arrived
        setIsTraveling(false);
        setGameData(prev => ({
          ...prev,
          ship: {
            ...prev.ship,
            fuel: { ...prev.ship.fuel, current: prev.ship.fuel.current - fuelCost }
          },
          currentSystemId: targetSystem.id,
          gameTurn: prev.gameTurn + Math.ceil(distance / 100) // 1 turn per 100 pixels
        }));
        toast({ title: "Arrived", description: `You have arrived at ${targetSystem.name}.`});
        setGameState('system-view');
      }
    };

    animationFrameRef.current = requestAnimationFrame(animate);

  }, [currentSystem, gameData.ship.fuel, isTraveling, gameIsPaused, fuelConsumptionRate, setGameData, toast, setGameState]);

  const handleSystemClick = useCallback((system: StarSystem) => {
    if (isTraveling || gameIsPaused) return;

    if (system.id === gameData.currentSystemId) {
      setGameState('system-view');
    } else {
      handleTravel(system);
    }
  }, [isTraveling, gameData.currentSystemId, setGameState, handleTravel, gameIsPaused]);

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);
  
  return (
    <div className="relative h-full w-full bg-background overflow-hidden">
      <GameCanvas 
        systems={gameData.starmap.systems}
        onSystemClick={handleSystemClick}
        shipPosition={shipDisplayPosition}
        travelRange={travelRange}
      />
      {!gameIsPaused && !isTraveling && <Hud gameData={gameData} setGameState={setGameState} isPaused={isPaused} togglePause={() => setIsPaused(!isPaused)} handleQuit={handleQuit} />}
      
      {isTraveling && (
        <div className="absolute bottom-10 left-1/2 -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-10 pointer-events-none">
            <p className="font-headline tracking-widest animate-pulse">TRAVELING...</p>
        </div>
      )}

      {gameIsPaused && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center flex-col gap-4 z-50 backdrop-blur-sm">
              <h1 className="text-5xl font-headline text-primary">PAUSED</h1>
              {!isPausedByPlatform ? 
                <Button onClick={() => setIsPaused(false)} variant="secondary" size="lg" className="text-xl">Resume</Button>
                : <p className="text-muted-foreground">Resume from the platform to continue.</p>
              }
          </div>
      )}
    </div>
  );
}
