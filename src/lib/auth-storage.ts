'use client';

/**
 * Secure token storage utility
 * Provides a secure way to store and retrieve authentication tokens
 * with proper error handling and validation
 */

const TOKEN_STORAGE_KEY = 'bedrock-token';
const REFRESH_TOKEN_STORAGE_KEY = 'bedrock-refresh-token';
const TOKEN_EXPIRY_KEY = 'bedrock-token-expiry';

export interface TokenData {
  token: string;
  refreshToken?: string;
  expiresAt?: number;
}

export class AuthStorage {
  private static isClient(): boolean {
    return typeof window !== 'undefined';
  }

  private static isValidToken(token: string): boolean {
    if (!token || typeof token !== 'string') return false;

    // Basic JWT format validation (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    try {
      // Validate that header and payload parts are valid base64
      // Note: signature part can be empty for unsigned tokens
      for (let i = 0; i < 2; i++) {
        const part = parts[i];
        if (!part) return false;
        // Add padding if needed for base64 decoding
        const padded = part + '='.repeat((4 - part.length % 4) % 4);
        atob(padded.replace(/-/g, '+').replace(/_/g, '/'));
      }
      return true;
    } catch {
      return false;
    }
  }

  private static sanitizeToken(token: string): string {
    // Remove any potential XSS characters and whitespace
    return token.trim().replace(/[<>'"&]/g, '');
  }

  /**
   * Store authentication tokens securely
   */
  static setTokens(tokenData: TokenData): boolean {
    if (!this.isClient()) {
      console.warn('AuthStorage: Cannot store tokens on server side');
      return false;
    }

    try {
      const { token, refreshToken, expiresAt } = tokenData;
      
      if (!token) {
        console.error('AuthStorage: No token provided');
        return false;
      }

      if (!this.isValidToken(token)) {
        console.error('AuthStorage: Invalid token format provided');
        return false;
      }

      const sanitizedToken = this.sanitizeToken(token);
      localStorage.setItem(TOKEN_STORAGE_KEY, sanitizedToken);

      if (refreshToken && this.isValidToken(refreshToken)) {
        const sanitizedRefreshToken = this.sanitizeToken(refreshToken);
        localStorage.setItem(REFRESH_TOKEN_STORAGE_KEY, sanitizedRefreshToken);
      }

      if (expiresAt && typeof expiresAt === 'number' && expiresAt > Date.now()) {
        localStorage.setItem(TOKEN_EXPIRY_KEY, expiresAt.toString());
      }

      return true;
    } catch (error) {
      console.error('AuthStorage: Error storing tokens:', error);
      return false;
    }
  }

  /**
   * Retrieve the access token
   */
  static getToken(): string | null {
    if (!this.isClient()) return null;

    try {
      const token = localStorage.getItem(TOKEN_STORAGE_KEY);
      if (!token) {
        return null; // No token stored, don't log error
      }

      if (!this.isValidToken(token)) {
        this.clearTokens(); // Clear invalid tokens
        return null;
      }

      // Check if token is expired
      const expiryStr = localStorage.getItem(TOKEN_EXPIRY_KEY);
      if (expiryStr) {
        const expiry = parseInt(expiryStr, 10);
        if (expiry <= Date.now()) {
          this.clearTokens(); // Clear expired tokens
          return null;
        }
      }

      return token;
    } catch (error) {
      console.error('AuthStorage: Error retrieving token:', error);
      this.clearTokens();
      return null;
    }
  }

  /**
   * Retrieve the refresh token
   */
  static getRefreshToken(): string | null {
    if (!this.isClient()) return null;

    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_STORAGE_KEY);
      if (!refreshToken || !this.isValidToken(refreshToken)) {
        return null;
      }
      return refreshToken;
    } catch (error) {
      console.error('AuthStorage: Error retrieving refresh token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return this.getToken() !== null;
  }

  /**
   * Clear all stored tokens
   */
  static clearTokens(): void {
    if (!this.isClient()) return;

    try {
      localStorage.removeItem(TOKEN_STORAGE_KEY);
      localStorage.removeItem(REFRESH_TOKEN_STORAGE_KEY);
      localStorage.removeItem(TOKEN_EXPIRY_KEY);
    } catch (error) {
      console.error('AuthStorage: Error clearing tokens:', error);
    }
  }

  /**
   * Get token expiry time
   */
  static getTokenExpiry(): number | null {
    if (!this.isClient()) return null;

    try {
      const expiryStr = localStorage.getItem(TOKEN_EXPIRY_KEY);
      return expiryStr ? parseInt(expiryStr, 10) : null;
    } catch {
      return null;
    }
  }

  /**
   * Check if token will expire soon (within 5 minutes)
   */
  static isTokenExpiringSoon(): boolean {
    const expiry = this.getTokenExpiry();
    if (!expiry) return false;
    
    const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
    return expiry <= fiveMinutesFromNow;
  }
}

// Legacy support - maintain backward compatibility
export const getStoredToken = () => AuthStorage.getToken();
export const clearStoredToken = () => AuthStorage.clearTokens();
export const isAuthenticated = () => AuthStorage.isAuthenticated();
