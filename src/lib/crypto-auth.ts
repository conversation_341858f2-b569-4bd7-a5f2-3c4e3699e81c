'use client';

import { AuthStorage } from './auth-storage';

/**
 * Crypto wallet authentication utilities
 * Provides functionality to authenticate users with their crypto wallets
 */

// Your authorized wallet address
const AUTHORIZED_WALLET = '******************************************';

// Ethereum provider interface
interface EthereumProvider {
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  on: (event: string, callback: (accounts: string[]) => void) => void;
  removeListener: (event: string, callback: (accounts: string[]) => void) => void;
  isMetaMask?: boolean;
  isConnected?: () => boolean;
}

declare global {
  interface Window {
    ethereum?: EthereumProvider;
  }
}

export interface WalletConnectionResult {
  success: boolean;
  address?: string;
  error?: string;
}

/**
 * Check if MetaMask or compatible wallet is available
 */
export function isWalletAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.ethereum;
}

/**
 * Get the current connected wallet address
 */
export async function getCurrentWalletAddress(): Promise<string | null> {
  if (!isWalletAvailable()) return null;

  try {
    const accounts = await window.ethereum!.request({
      method: 'eth_accounts'
    });
    return accounts.length > 0 ? accounts[0] : null;
  } catch (error) {
    console.error('Error getting wallet address:', error);
    return null;
  }
}

/**
 * Connect to wallet and request account access
 */
export async function connectWallet(): Promise<WalletConnectionResult> {
  if (!isWalletAvailable()) {
    return {
      success: false,
      error: 'No crypto wallet detected. Please install MetaMask or a compatible wallet.'
    };
  }

  try {
    const accounts = await window.ethereum!.request({
      method: 'eth_requestAccounts'
    });

    if (accounts.length === 0) {
      return {
        success: false,
        error: 'No accounts found. Please unlock your wallet.'
      };
    }

    const address = accounts[0];
    return {
      success: true,
      address: address.toLowerCase()
    };
  } catch (error: any) {
    console.error('Error connecting wallet:', error);
    
    if (error.code === 4001) {
      return {
        success: false,
        error: 'Connection rejected by user.'
      };
    }
    
    return {
      success: false,
      error: 'Failed to connect wallet. Please try again.'
    };
  }
}

/**
 * Check if the connected wallet is authorized
 */
export function isAuthorizedWallet(address: string): boolean {
  return address.toLowerCase() === AUTHORIZED_WALLET.toLowerCase();
}

/**
 * Sign a message with the wallet for authentication
 */
export async function signAuthMessage(address: string): Promise<{ success: boolean; signature?: string; error?: string }> {
  if (!isWalletAvailable()) {
    return { success: false, error: 'No wallet available' };
  }

  try {
    const message = `Sign this message to authenticate with Starship Voyager.\n\nAddress: ${address}\nTimestamp: ${Date.now()}`;
    
    const signature = await window.ethereum!.request({
      method: 'personal_sign',
      params: [message, address]
    });

    return {
      success: true,
      signature
    };
  } catch (error: any) {
    console.error('Error signing message:', error);
    
    if (error.code === 4001) {
      return {
        success: false,
        error: 'Signature rejected by user.'
      };
    }
    
    return {
      success: false,
      error: 'Failed to sign message. Please try again.'
    };
  }
}

/**
 * Authenticate with crypto wallet
 */
export async function authenticateWithWallet(): Promise<{ success: boolean; error?: string }> {
  try {
    // Connect to wallet
    const connectionResult = await connectWallet();
    if (!connectionResult.success || !connectionResult.address) {
      return {
        success: false,
        error: connectionResult.error || 'Failed to connect wallet'
      };
    }

    const address = connectionResult.address;

    // Check if wallet is authorized
    if (!isAuthorizedWallet(address)) {
      return {
        success: false,
        error: 'This wallet address is not authorized to access the game.'
      };
    }

    // Sign authentication message
    const signResult = await signAuthMessage(address);
    if (!signResult.success) {
      return {
        success: false,
        error: signResult.error || 'Failed to sign authentication message'
      };
    }

    // Create a mock JWT token for the authorized wallet (proper JWT format)
    const header = btoa(JSON.stringify({ alg: 'none', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({
      address: address,
      signature: signResult.signature,
      timestamp: Date.now(),
      authorized: true,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    }));
    // For unsigned tokens, use empty string for signature part
    const mockToken = `${header}.${payload}.`;

    // Store the token
    const tokenStored = AuthStorage.setTokens({
      token: mockToken,
      expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    });

    if (!tokenStored) {
      return {
        success: false,
        error: 'Failed to store authentication token'
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Wallet authentication error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred during wallet authentication'
    };
  }
}

/**
 * Check if user is authenticated via wallet
 */
export function isWalletAuthenticated(): boolean {
  const token = AuthStorage.getToken();
  if (!token) return false;

  try {
    // Parse JWT token (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    const payload = JSON.parse(atob(parts[1]));
    return payload.authorized === true && isAuthorizedWallet(payload.address);
  } catch {
    return false;
  }
}

/**
 * Get authenticated wallet address from stored token
 */
export function getAuthenticatedWalletAddress(): string | null {
  const token = AuthStorage.getToken();
  if (!token) return null;

  try {
    // Parse JWT token (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    const payload = JSON.parse(atob(parts[1]));
    return payload.authorized === true ? payload.address : null;
  } catch {
    return null;
  }
}

/**
 * Listen for wallet account changes
 */
export function onWalletAccountChange(callback: (accounts: string[]) => void): () => void {
  if (!isWalletAvailable()) {
    return () => {};
  }

  const handleAccountsChanged = (accounts: string[]) => {
    callback(accounts);
  };

  window.ethereum!.on('accountsChanged', handleAccountsChanged);

  return () => {
    if (window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
    }
  };
}

/**
 * Disconnect wallet authentication
 */
export function disconnectWallet(): void {
  AuthStorage.clearTokens();
}
