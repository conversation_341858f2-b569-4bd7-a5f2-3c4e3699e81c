/**
 * Input validation and sanitization utilities
 * Provides comprehensive validation and sanitization for all user inputs
 */

import { z } from 'zod';

// Common validation patterns
const PATTERNS = {
  // Allow letters, numbers, spaces, and common punctuation, but prevent XSS
  SAFE_TEXT: /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/,
  // More restrictive for names (letters, spaces, hyphens, apostrophes)
  NAME: /^[a-zA-Z\s\-']+$/,
  // Ship names can include numbers and more symbols
  SHIP_NAME: /^[a-zA-Z0-9\s\-_.,!()[\]]+$/,
  // Alphanumeric with underscores and hyphens
  IDENTIFIER: /^[a-zA-Z0-9_-]+$/,
} as const;

// Maximum lengths for different input types
const MAX_LENGTHS = {
  NAME: 50,
  SHIP_NAME: 100,
  DESCRIPTION: 500,
  IDENTIFIER: 30,
} as const;

/**
 * Sanitize text input by removing potentially dangerous characters
 */
export function sanitizeText(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    // Remove null bytes and control characters
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove script-like content
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    // Remove excessive whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Sanitize and validate player name
 */
export function validatePlayerName(name: string): { isValid: boolean; sanitized: string; error?: string } {
  const sanitized = sanitizeText(name);
  
  if (!sanitized) {
    return { isValid: false, sanitized: '', error: 'Name is required' };
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, sanitized, error: 'Name must be at least 2 characters long' };
  }
  
  if (sanitized.length > MAX_LENGTHS.NAME) {
    return { isValid: false, sanitized, error: `Name must be no more than ${MAX_LENGTHS.NAME} characters long` };
  }
  
  if (!PATTERNS.NAME.test(sanitized)) {
    return { isValid: false, sanitized, error: 'Name can only contain letters, spaces, hyphens, and apostrophes' };
  }
  
  return { isValid: true, sanitized };
}

/**
 * Sanitize and validate ship name
 */
export function validateShipName(name: string): { isValid: boolean; sanitized: string; error?: string } {
  const sanitized = sanitizeText(name);
  
  if (!sanitized) {
    return { isValid: false, sanitized: '', error: 'Ship name is required' };
  }
  
  if (sanitized.length < 3) {
    return { isValid: false, sanitized, error: 'Ship name must be at least 3 characters long' };
  }
  
  if (sanitized.length > MAX_LENGTHS.SHIP_NAME) {
    return { isValid: false, sanitized, error: `Ship name must be no more than ${MAX_LENGTHS.SHIP_NAME} characters long` };
  }
  
  if (!PATTERNS.SHIP_NAME.test(sanitized)) {
    return { isValid: false, sanitized, error: 'Ship name contains invalid characters' };
  }
  
  return { isValid: true, sanitized };
}

/**
 * Validate race selection
 */
export function validateRace(race: string, validRaces: string[]): { isValid: boolean; error?: string } {
  if (!race || typeof race !== 'string') {
    return { isValid: false, error: 'Race selection is required' };
  }
  
  const sanitizedRace = race.trim().toLowerCase();
  if (!validRaces.includes(sanitizedRace)) {
    return { isValid: false, error: 'Invalid race selection' };
  }
  
  return { isValid: true };
}

/**
 * Sanitize numeric input
 */
export function sanitizeNumber(input: any, min?: number, max?: number): { isValid: boolean; value: number; error?: string } {
  const num = Number(input);
  
  if (isNaN(num) || !isFinite(num)) {
    return { isValid: false, value: 0, error: 'Invalid number' };
  }
  
  if (min !== undefined && num < min) {
    return { isValid: false, value: num, error: `Value must be at least ${min}` };
  }
  
  if (max !== undefined && num > max) {
    return { isValid: false, value: num, error: `Value must be no more than ${max}` };
  }
  
  return { isValid: true, value: num };
}

/**
 * Validate and sanitize game data before storage
 */
export function validateGameData(data: any): { isValid: boolean; sanitized?: any; errors: string[] } {
  const errors: string[] = [];
  
  if (!data || typeof data !== 'object') {
    return { isValid: false, errors: ['Invalid game data format'] };
  }
  
  const sanitized = { ...data };
  
  // Validate player data if present
  if (data.player) {
    if (data.player.name) {
      const nameValidation = validatePlayerName(data.player.name);
      if (!nameValidation.isValid) {
        errors.push(`Player name: ${nameValidation.error}`);
      } else {
        sanitized.player.name = nameValidation.sanitized;
      }
    }
    
    if (data.player.shipName) {
      const shipNameValidation = validateShipName(data.player.shipName);
      if (!shipNameValidation.isValid) {
        errors.push(`Ship name: ${shipNameValidation.error}`);
      } else {
        sanitized.player.shipName = shipNameValidation.sanitized;
      }
    }
  }
  
  // Validate numeric values
  const numericFields = ['gameTurn', 'score', 'highScore'];
  numericFields.forEach(field => {
    if (data[field] !== undefined) {
      const numValidation = sanitizeNumber(data[field], 0);
      if (!numValidation.isValid) {
        errors.push(`${field}: ${numValidation.error}`);
      } else {
        sanitized[field] = numValidation.value;
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    sanitized: errors.length === 0 ? sanitized : undefined,
    errors
  };
}

/**
 * Enhanced Zod schemas with sanitization
 */
export const validationSchemas = {
  playerName: z.string()
    .transform(sanitizeText)
    .refine(name => validatePlayerName(name).isValid, {
      message: 'Invalid player name'
    }),
    
  shipName: z.string()
    .transform(sanitizeText)
    .refine(name => validateShipName(name).isValid, {
      message: 'Invalid ship name'
    }),
    
  race: z.string()
    .transform(str => str.trim().toLowerCase()),
    
  gameScore: z.number()
    .int()
    .min(0, 'Score cannot be negative'),
    
  gameTurn: z.number()
    .int()
    .min(0, 'Turn cannot be negative'),
};

/**
 * Sanitize object recursively
 */
export function deepSanitize(obj: any): any {
  if (obj === null || obj === undefined) return obj;
  
  if (typeof obj === 'string') {
    return sanitizeText(obj);
  }
  
  if (typeof obj === 'number') {
    return isFinite(obj) ? obj : 0;
  }
  
  if (typeof obj === 'boolean') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(deepSanitize);
  }
  
  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeText(key);
      if (sanitizedKey) {
        sanitized[sanitizedKey] = deepSanitize(value);
      }
    }
    return sanitized;
  }
  
  return obj;
}
